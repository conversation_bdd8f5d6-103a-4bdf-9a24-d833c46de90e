import { IndustrialProtocol, ProtocolType } from '../types';
import { ModbusProtocol } from './ModbusProtocol';
import { OPCUAProtocol } from './OPCUAProtocol';
import { MQTTProtocol } from './MQTTProtocol';
import { EtherCATProtocol } from './EtherCATProtocol';
import { Debug } from '../../utils/Debug';

/**
 * 工业通信协议工厂类
 * 负责创建和管理各种工业通信协议实例
 */
export class ProtocolFactory {
  private static instance: ProtocolFactory;
  private protocols: Map<ProtocolType, () => IndustrialProtocol> = new Map();
  private protocolInstances: Map<ProtocolType, IndustrialProtocol> = new Map();

  /**
   * 获取工厂单例实例
   * @returns 协议工厂实例
   */
  public static getInstance(): ProtocolFactory {
    if (!ProtocolFactory.instance) {
      ProtocolFactory.instance = new ProtocolFactory();
    }
    return ProtocolFactory.instance;
  }

  /**
   * 私有构造函数，注册默认协议
   */
  private constructor() {
    this.registerDefaultProtocols();
  }

  /**
   * 注册默认协议
   */
  private registerDefaultProtocols(): void {
    // 注册Modbus协议
    this.registerProtocol(ProtocolType.MODBUS_TCP, () => new ModbusProtocol());
    this.registerProtocol(ProtocolType.MODBUS_RTU, () => new ModbusProtocol());
    
    // 注册OPC UA协议
    this.registerProtocol(ProtocolType.OPC_UA, () => new OPCUAProtocol());
    
    // 注册MQTT协议
    this.registerProtocol(ProtocolType.MQTT, () => new MQTTProtocol());
    
    // 注册EtherCAT协议
    this.registerProtocol(ProtocolType.ETHERCAT, () => new EtherCATProtocol());

    Debug.log('ProtocolFactory', '默认工业通信协议已注册');
  }

  /**
   * 注册协议
   * @param type 协议类型
   * @param factory 协议工厂函数
   */
  public registerProtocol(type: ProtocolType, factory: () => IndustrialProtocol): void {
    this.protocols.set(type, factory);
    Debug.log('ProtocolFactory', `协议已注册: ${type}`);
  }

  /**
   * 取消注册协议
   * @param type 协议类型
   */
  public unregisterProtocol(type: ProtocolType): void {
    this.protocols.delete(type);
    this.protocolInstances.delete(type);
    Debug.log('ProtocolFactory', `协议已取消注册: ${type}`);
  }

  /**
   * 创建协议实例
   * @param type 协议类型
   * @param singleton 是否使用单例模式（默认true）
   * @returns 协议实例
   */
  public createProtocol(type: ProtocolType, singleton: boolean = true): IndustrialProtocol {
    const factory = this.protocols.get(type);
    if (!factory) {
      throw new Error(`不支持的协议类型: ${type}`);
    }

    if (singleton) {
      // 单例模式：每种协议类型只创建一个实例
      let instance = this.protocolInstances.get(type);
      if (!instance) {
        instance = factory();
        this.protocolInstances.set(type, instance);
        Debug.log('ProtocolFactory', `创建协议单例实例: ${type}`);
      }
      return instance;
    } else {
      // 非单例模式：每次都创建新实例
      const instance = factory();
      Debug.log('ProtocolFactory', `创建协议新实例: ${type}`);
      return instance;
    }
  }

  /**
   * 获取协议实例（如果存在）
   * @param type 协议类型
   * @returns 协议实例或undefined
   */
  public getProtocol(type: ProtocolType): IndustrialProtocol | undefined {
    return this.protocolInstances.get(type);
  }

  /**
   * 检查协议是否已注册
   * @param type 协议类型
   * @returns 是否已注册
   */
  public isProtocolRegistered(type: ProtocolType): boolean {
    return this.protocols.has(type);
  }

  /**
   * 获取所有已注册的协议类型
   * @returns 协议类型数组
   */
  public getRegisteredProtocols(): ProtocolType[] {
    return Array.from(this.protocols.keys());
  }

  /**
   * 获取所有协议实例
   * @returns 协议实例数组
   */
  public getAllProtocolInstances(): IndustrialProtocol[] {
    return Array.from(this.protocolInstances.values());
  }

  /**
   * 销毁协议实例
   * @param type 协议类型
   */
  public destroyProtocol(type: ProtocolType): void {
    const instance = this.protocolInstances.get(type);
    if (instance) {
      // 如果协议实例有销毁方法，调用它
      if (typeof (instance as any).destroy === 'function') {
        (instance as any).destroy();
      }
      this.protocolInstances.delete(type);
      Debug.log('ProtocolFactory', `协议实例已销毁: ${type}`);
    }
  }

  /**
   * 销毁所有协议实例
   */
  public destroyAllProtocols(): void {
    for (const type of this.protocolInstances.keys()) {
      this.destroyProtocol(type);
    }
    Debug.log('ProtocolFactory', '所有协议实例已销毁');
  }

  /**
   * 获取协议信息
   * @param type 协议类型
   * @returns 协议信息
   */
  public getProtocolInfo(type: ProtocolType): ProtocolInfo | null {
    if (!this.isProtocolRegistered(type)) {
      return null;
    }

    const instance = this.getProtocol(type);
    
    return {
      type,
      name: this.getProtocolName(type),
      description: this.getProtocolDescription(type),
      isInstantiated: !!instance,
      features: this.getProtocolFeatures(type)
    };
  }

  /**
   * 获取所有协议信息
   * @returns 协议信息数组
   */
  public getAllProtocolInfo(): ProtocolInfo[] {
    return this.getRegisteredProtocols().map(type => this.getProtocolInfo(type)!);
  }

  /**
   * 获取协议名称
   * @param type 协议类型
   * @returns 协议名称
   */
  private getProtocolName(type: ProtocolType): string {
    switch (type) {
      case ProtocolType.MODBUS_TCP:
        return 'Modbus TCP';
      case ProtocolType.MODBUS_RTU:
        return 'Modbus RTU';
      case ProtocolType.OPC_UA:
        return 'OPC UA';
      case ProtocolType.MQTT:
        return 'MQTT';
      case ProtocolType.ETHERCAT:
        return 'EtherCAT';
      default:
        return type;
    }
  }

  /**
   * 获取协议描述
   * @param type 协议类型
   * @returns 协议描述
   */
  private getProtocolDescription(type: ProtocolType): string {
    switch (type) {
      case ProtocolType.MODBUS_TCP:
        return 'Modbus TCP/IP协议，基于以太网的工业通信协议';
      case ProtocolType.MODBUS_RTU:
        return 'Modbus RTU协议，基于串行通信的工业协议';
      case ProtocolType.OPC_UA:
        return 'OPC统一架构，现代工业4.0通信标准';
      case ProtocolType.MQTT:
        return 'MQTT发布/订阅协议，适用于物联网设备通信';
      case ProtocolType.ETHERCAT:
        return 'EtherCAT实时以太网协议，高性能运动控制通信';
      default:
        return '未知协议';
    }
  }

  /**
   * 获取协议特性
   * @param type 协议类型
   * @returns 协议特性
   */
  private getProtocolFeatures(type: ProtocolType): string[] {
    switch (type) {
      case ProtocolType.MODBUS_TCP:
        return ['TCP/IP', '客户端/服务器', '轮询', '简单易用'];
      case ProtocolType.MODBUS_RTU:
        return ['串行通信', '主从模式', '轮询', '广泛支持'];
      case ProtocolType.OPC_UA:
        return ['安全性', '互操作性', '可扩展性', '发布/订阅'];
      case ProtocolType.MQTT:
        return ['发布/订阅', '轻量级', '物联网', '消息队列'];
      case ProtocolType.ETHERCAT:
        return ['实时性', '高性能', '运动控制', '确定性'];
      default:
        return [];
    }
  }

  /**
   * 重置工厂（用于测试）
   */
  public reset(): void {
    this.destroyAllProtocols();
    this.protocols.clear();
    this.protocolInstances.clear();
    this.registerDefaultProtocols();
    Debug.log('ProtocolFactory', '协议工厂已重置');
  }
}

/**
 * 协议信息接口
 */
export interface ProtocolInfo {
  /** 协议类型 */
  type: ProtocolType;
  /** 协议名称 */
  name: string;
  /** 协议描述 */
  description: string;
  /** 是否已实例化 */
  isInstantiated: boolean;
  /** 协议特性 */
  features: string[];
}

/**
 * 获取协议工厂实例的便捷函数
 * @returns 协议工厂实例
 */
export function getProtocolFactory(): ProtocolFactory {
  return ProtocolFactory.getInstance();
}

/**
 * 创建协议实例的便捷函数
 * @param type 协议类型
 * @param singleton 是否使用单例模式
 * @returns 协议实例
 */
export function createProtocol(type: ProtocolType, singleton: boolean = true): IndustrialProtocol {
  return getProtocolFactory().createProtocol(type, singleton);
}
